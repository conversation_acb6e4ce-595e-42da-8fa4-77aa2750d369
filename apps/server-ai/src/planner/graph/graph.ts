import {
  StateGraph,
  Annotation,
  START,
  END,
  interrupt,
  Command,
  MemorySaver,
  messagesStateReducer,
} from '@langchain/langgraph';
import { AIMessage, BaseMessage, HumanMessage } from '@langchain/core/messages';
import { ChatOpenAI } from '@langchain/openai';
import { tool } from '@langchain/core/tools';
import { z } from 'zod';
import { fetchDocumentWithImages } from './openapi.js';

// 定义节点名称枚举
export enum NodeNames {
  COORDINATOR = 'coordinator',
  TOOLS = 'tools',
  PRD_PARSER = 'prdParser',
  PRD_FEEDBACK = 'prdFeedbackNode',
  PLANNER = 'planner',
  PLANNER_FEEDBACK = 'plannerFeedback',
}

// 定义工具名称枚举
export enum ToolNames {
  HANDOFF_TO_PLANNER = 'handoff_to_planner',
  FETCH_DOCUMENT = 'fetch_document',
}

// 定义图片信息接口
interface ImageInfo {
  url: string;
  base64: string;
  error?: string;
}

// 定义状态结构
const StateAnnotation = Annotation.Root({
  originalPRD: Annotation<string>,
  structuredPRD: Annotation<string>,
  taskJSON: Annotation<string>,
  prdFeedback: Annotation<string>,
  prdAnalysis: Annotation<string>,
  planFeedback: Annotation<string>,
  isPRDRequest: Annotation<boolean>,
  documentContent: Annotation<string>,
  // 新增图片相关状态
  images: Annotation<ImageInfo[]>({
    reducer: (x, y) => x.concat(y),
    default: () => [],
  }),
  messages: Annotation<(BaseMessage | AIMessage)[]>({
    reducer: messagesStateReducer,
    default: () => [],
  }),
});

// 创建包含图片的消息内容
function createMessageWithImages(
  textContent: string,
  images: ImageInfo[],
): any[] {
  const content: any[] = [
    {
      type: 'text',
      text: textContent,
    },
  ];

  // 添加成功处理的图片
  images.forEach((image) => {
    if (image.base64 && !image.error) {
      content.push({
        type: 'image_url',
        image_url: {
          url: image.base64,
        },
      });
    }
  });

  return content;
}

// 创建 LLM 实例
function createLLM() {
  return new ChatOpenAI({
    model: 'gpt-4o',
    apiKey: process.env.OPENAI_API_KEY || 'TXI1OjwcUJORWR',
    timeout: 60000, // 60秒超时
    maxRetries: 3, // 最多重试3次
    configuration: {
      baseURL: 'https://ai-gateway.corp.kuaishou.com/v2',
      defaultHeaders: {
        'x-dmo-provider': 'openai',
      },
    },
  });
}

// 创建工具
function createTools() {
  // 定义 handoff_to_planner 工具
  const handoffToPlannerTool = tool(
    ({ reason }: { reason: string }) => {
      console.log('Handoff to planner tool called with reason:', reason);
      return;
    },
    {
      name: ToolNames.HANDOFF_TO_PLANNER,
      description:
        'Hand off the conversation to the planner when a PRD request is identified',
      schema: z.object({
        reason: z
          .string()
          .describe('The reason for handing off to the planner'),
      }),
    },
  );

  // 定义 fetch_document 工具
  const fetchDocumentTool = tool(
    async ({ url }: { url: string }) => {
      console.log('Fetch document tool called with URL:', url);
      const { content } = await fetchDocumentWithImages(url);
      return content;
    },
    {
      name: ToolNames.FETCH_DOCUMENT,
      description:
        'Fetch content from a document URL when user provides a link',
      schema: z.object({
        url: z.string().describe('The URL of the document to fetch'),
      }),
    },
  );

  return [handoffToPlannerTool, fetchDocumentTool];
}

// 工具节点 - 处理工具执行
async function toolsNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  // 获取最后一条消息中的工具调用
  const lastMessage = state.messages[state.messages.length - 1] as AIMessage;
  console.log('Tools节点开始执行工具...', state.messages, lastMessage);
  if (!lastMessage?.tool_calls || lastMessage.tool_calls.length === 0) {
    console.log('没有找到工具调用，返回coordinator');
    return new Command({
      goto: NodeNames.COORDINATOR,
    });
  }

  const toolCall = lastMessage.tool_calls[0];
  console.log('执行工具:', toolCall.name, toolCall.args);

  try {
    // 根据工具名称执行相应的工具
    if (toolCall.name === ToolNames.FETCH_DOCUMENT) {
      const { url } = toolCall.args as { url: string };

      // 使用增强版文档获取功能，支持图片处理
      const { content, images } = await fetchDocumentWithImages(url);

      // 返回到coordinator进行PRD判断
      return new Command({
        goto: NodeNames.COORDINATOR,
        update: {
          documentContent: content,
          images: images,
        },
      });
    } else if (toolCall.name === ToolNames.HANDOFF_TO_PLANNER) {
      console.log('handoff_to_planner工具调用，路由到PRD_PARSER节点');
      return new Command({
        goto: NodeNames.PRD_PARSER,
        update: {
          isPRDRequest: true,
        },
      });
    }

    // 未知工具，返回coordinator
    console.log('未知工具:', toolCall.name);
    return new Command({
      goto: NodeNames.COORDINATOR,
    });
  } catch (error) {
    console.error('工具执行失败:', error);

    // 如果是fetch_document失败，设置错误信息并返回coordinator
    if (toolCall.name === ToolNames.FETCH_DOCUMENT) {
      return new Command({
        goto: NodeNames.COORDINATOR,
        update: {
          documentContent: `获取文档失败: ${error.message}`,
        },
      });
    }

    // 其他错误，直接返回coordinator
    return new Command({
      goto: NodeNames.COORDINATOR,
    });
  }
}

// Coordinator节点 - 判断是否为PRD需求
async function coordinatorNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('Coordinator节点开始判断用户输入...', state.originalPRD);

  const llm = createLLM();
  const tools = createTools();
  const coordinatorLLM = llm.bindTools(tools);

  // 检查最新消息是否包含图片
  const lastMessage = state.messages[state.messages.length - 1];
  const hasImage =
    lastMessage &&
    Array.isArray(lastMessage.content) &&
    lastMessage.content.some((item) => item.type === 'image_url');

  // 检查是否有处理过的图片数据
  const hasProcessedImages = state.images && state.images.length > 0;

  // 如果已经有文档内容，说明是从工具节点返回的，直接进行PRD判断
  if (state.documentContent) {
    console.log('检测到已获取的文档内容，进行PRD判断');
    const combinedInput = `${state.originalPRD}\n\n获取的文档内容：\n${state.documentContent}`;

    const prdJudgmentPromptText = `
作为智能助手协调员，请判断以下内容是否为产品需求文档(PRD)相关的请求：

用户输入和文档内容：
${combinedInput}

判断标准：
1. 是否包含产品功能描述
2. 是否包含需求说明
3. 是否要求进行产品规划或任务分解
4. 是否涉及产品开发相关内容

如果是PRD相关请求，请调用 handoff_to_planner 工具并提供判断理由。
如果不是PRD请求，请直接回复礼貌的说明信息。
`;

    // 如果有图片信息，使用包含图片的消息格式
    let response: AIMessage;
    if (state.images && state.images.length > 0) {
      const messageContent = createMessageWithImages(
        prdJudgmentPromptText,
        state.images,
      );
      console.log(
        'coordinatorNode: 使用包含图片的消息格式',
        state.images.length,
        messageContent,
      );
      response = await coordinatorLLM.invoke([
        new HumanMessage({
          content: messageContent,
        }),
      ]);
    } else {
      response = await coordinatorLLM.invoke(prdJudgmentPromptText);
    }

    if (response.tool_calls && response.tool_calls.length > 0) {
      const toolCall = response.tool_calls[0];
      if (toolCall.name === ToolNames.HANDOFF_TO_PLANNER) {
        console.log('识别为PRD请求，路由到PRD_PARSER节点');
        return new Command({
          goto: NodeNames.PRD_PARSER,
          update: {
            isPRDRequest: true,
            originalPRD: combinedInput, // 更新为包含文档内容的完整输入
          },
        });
      }
    }

    // 不是PRD请求
    const userMessage =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    return new Command({
      goto: END,
      update: {
        isPRDRequest: false,
        messages: [
          new AIMessage({
            content: userMessage,
            name: 'coordinator',
          }),
        ],
      },
    });
  }

  // 初次判断：检查是否包含文档链接、图片链接或是否为PRD请求
  const promptText = `
作为智能助手协调员，请判断以下用户输入是否为产品需求文档(PRD)相关的请求，或者是否包含需要处理的链接：

用户输入：
${state.originalPRD}

${hasImage ? '注意：用户消息中包含图片内容，请考虑图片在PRD分析中的作用。' : ''}
${hasProcessedImages ? `已处理的图片信息：${state.images.map((img) => `${img.url}${img.error ? ` (处理失败: ${img.error})` : ' (已转换为base64)'}`).join(', ')}` : ''}

判断标准：
1. 如果用户提供了文档链接（http/https URL），请调用 fetch_document 工具获取文档内容
3. 是否包含产品功能描述
4. 是否包含需求说明
5. 是否要求进行产品规划或任务分解
6. 是否涉及产品开发相关内容

处理逻辑：
- 如果包含文档链接，先调用 fetch_document 工具获取内容
- 如果是PRD相关请求，请调用 handoff_to_planner 工具并提供判断理由
- 如果不是PRD请求，请直接回复礼貌的说明信息

礼貌回复模板：
很抱歉，我是专门处理产品需求文档(PRD)分析和任务分解的助手。您的问题似乎不在我的专业范围内。

我的主要功能包括：
- 分析和结构化产品需求文档
- 将PRD转换为具体的开发任务
- 提供技术实现建议和项目规划
- 处理包含图片的产品需求文档

如果您有产品需求相关的问题，我很乐意为您提供帮助！
`;

  try {
    // 如果有图片信息，使用包含图片的消息格式
    let response: AIMessage;
    if (state.images && state.images.length > 0) {
      const messageContent = createMessageWithImages(promptText, state.images);
      response = await coordinatorLLM.invoke([
        new HumanMessage({
          content: messageContent,
        }),
      ]);
    } else {
      response = await coordinatorLLM.invoke(promptText);
    }
    console.log('Coordinator节点完成判断', response);

    // 检查是否有工具调用
    if (response.tool_calls && response.tool_calls.length > 0) {
      console.log('检测到工具调用，路由到工具节点');
      return new Command({
        goto: NodeNames.TOOLS,
        update: {
          messages: [response], // 将包含工具调用的响应传递给工具节点
        },
      });
    }

    // 如果没有工具调用，说明不是PRD请求
    console.log('非PRD请求，返回礼貌响应并结束');
    const userMessage =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    return new Command({
      goto: END,
      update: {
        isPRDRequest: false,
        messages: [
          new AIMessage({
            content: userMessage,
            name: 'coordinator',
          }),
        ],
      },
    });
  } catch (error) {
    console.error('Coordinator节点处理失败:', error);

    // 处理不同类型的错误
    let errorMessage = '系统暂时不可用，请稍后重试。';

    if (
      error.message.includes('AbortError') ||
      error.message.includes('Request was aborted')
    ) {
      errorMessage = '请求超时，请检查网络连接后重试。';
    } else if (error.message.includes('ENOTFOUND')) {
      errorMessage = '网络连接失败，请检查网络设置。';
    } else if (error.message.includes('401') || error.message.includes('403')) {
      errorMessage = 'API认证失败，请检查配置。';
    } else if (error.message.includes('429')) {
      errorMessage = '请求过于频繁，请稍后重试。';
    }

    // 返回错误响应而不是抛出异常
    return new Command({
      goto: END,
      update: {
        isPRDRequest: false,
        messages: [
          new AIMessage({
            content: errorMessage,
            name: 'coordinator',
          }),
        ],
      },
    });
  }
}

// PRD_PARSER节点 - 处理PRD
async function prdParserNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('PRD_PARSER节点开始处理原始PRD...');

  const llm = createLLM();

  // 构建包含图片的提示内容
  const promptText = `
你是一个专业的全栈工程师， 你能分析用户输入的产品需求描述是否满足前后端开发的要求，如果缺少了某些信息，你会提示用户进行补充：
如果用户输入的PRD信息完整，你会将其结构化为一个XML结构的文档。
如果PRD信息不完整，请使用amis生成一个form表单的json schema，以告知用户需要补充的信息，类似这样：
{
  "type": "form",
  "body": [
      {
        "name": "title",
        "type": "input-text",
        "label": "xxx"
      },
      {
        "name": "overview",
        "type": "textarea",
        "label": "xxx"
      }
  ]
}
-------
如果PRD信息完整，仅使用xml格式响应,类似这样：
<page>xxxxx</page>


${
  state.images && state.images.length > 0
    ? `
注意：用户提供了以下图片作为PRD的一部分：
${state.images.map((img, index) => `图片${index + 1}: ${img.url}${img.error ? ` (处理失败: ${img.error})` : ' (已处理)'}`).join('\n')}
请结合图片内容进行PRD分析。如果图片包含原型图、界面设计或功能说明，请在分析中考虑这些视觉信息。
`
    : ''
}

## 完整的需求PRD必须包含以下内容
* 前端：页面原型图、功能描述、组件间交互描述

## xml结构模版
页面结构如下:
提示区：
筛选区：
顶部搜索区：支持 select 也支持 inputText 或其他的表单控件。
筛选器区：每个筛选器占据一行，水平排列。选择器元素支持多种选择方式：级联选择、单项选择等
已选区域：展示当前的筛选情况。

表格区域：
列表头部： 包含各种操作、展示项
表格结构：数据列
表格底部：包含分页
以下是一个xml示例
<page class="data-management-system">
    <alert></alert>
    <!-- 筛选区域 -->
    <filterSection class="filter-container">
        <!-- 顶部搜索区 -->
        <searchArea class="search-container">
            <inputGroup>
                <select placeholder="选择搜索类型"/>
                <input type="text" placeholder="输入关键词搜索"/>
                <otherControls></otherControls>
            </inputGroup>
        </searchArea>

        <!-- 筛选器区 -->
        <filterArea class="filter-rows">
            <!-- 作者管理类目筛选行 -->
            <filterRow class="filter-row">
                <label>作者管理类目:</label>
                <horizontalSelect mode="multiple">
                    <option value="all">全部</option>
                    <option value="news">时政资讯</option>
                    <!-- 其他选项 -->
                </horizontalSelect>
            </filterRow>

            <!-- 视频词图类目筛选行 -->
            <filterRow class="filter-row">
                <label>视频词图类目:</label>
                <cascadeSelect>
                    <option value="all">全部</option>
                    <!-- 级联选项 -->
                </cascadeSelect>
            </filterRow>

            <!-- 高级筛选行 -->
            <otherFilterRow class="filter-row">
                <label>xxx:</label>
                <radioGroup>
                    <radio value="all">全部</radio>
                    <radio value="social">xx</radio>
                    <radio value="weather">xx</radio>
                </radioGroup>
            </otherFilterRow>

        </filterArea>

        <!-- 已选区域 -->
        <selectedFilters class="selected-tags">
            <tag closable>时政资讯</tag>
            <tag closable>社会事件</tag>
            <!-- 其他已选标签 -->
        </selectedFilters>
    </filterSection>

    <!-- 表格区域 -->
    <tableSection class="table-container">
        <!-- 表格头部 -->
        <tableHeader class="table-header">
            <div class="left">共 2 个视频</div>
            <div class="right">
                <button>下载明细</button>
            </div>
        </tableHeader>

        <!-- 表格主体 -->
        <table class="data-table">
            <thead>
                <tr>
                    <th>排名</th>
                    <th>视频</th>
                    <th>有效播放次数</th>
                    <th>点赞次数</th>
                    <th>评论次数</th>
                    <th>分享次数</th>
                    <th>收藏次数</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <!-- 数据行 -->
                <tr>
                    <td>1</td>
                    <td>
                        <videoCard>
                            <thumbnail/>
                            <videoInfo/>
                        </videoCard>
                    </td>
                    <!-- 其他数据列 -->
                    <td>
                        <actionButtons>
                            <button>视频详情</button>
                            <button>提报热点</button>
                            <!-- 其他操作按钮 -->
                        </actionButtons>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- 分页器 -->
        <pagination class="table-pagination">
            <pageSize/>
            <pageNumber/>
            <totalCount/>
        </pagination>
    </tableSection>
</page>
---------
以下是原始PRD：
${state.originalPRD}
`;

  try {
    // 如果有图片信息，使用包含图片的消息格式
    let response: AIMessage;
    if (state.images && state.images.length > 0) {
      const messageContent = createMessageWithImages(promptText, state.images);
      response = await llm.invoke([
        new HumanMessage({
          content: messageContent,
        }),
      ]);
    } else {
      response = await llm.invoke(promptText);
    }
    const responseContent =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    console.log('PRD-Parser节点完成PRD结构化', responseContent);

    // 解析XML响应
    const isComplete = responseContent.match(/xml/);

    // 根据完整性判断下一步
    if (isComplete) {
      console.log('PRD信息完整，返回完整PRD并进入Planner节点');
      return new Command({
        goto: NodeNames.PLANNER,
        update: {
          structuredPRD: responseContent,
        },
      });
    } else {
      console.log('PRD信息不完整，返回补充信息请求');
      return new Command({
        goto: NodeNames.PRD_FEEDBACK,
        update: {
          prdAnalysis: responseContent,
        },
      });
    }
  } catch (error) {
    console.error('PRD-Parser节点处理失败:', error);
    throw error;
  }
}

// 人工反馈节点 - PM阶段
function prdFeedbackNode(state: typeof StateAnnotation.State): Command {
  console.log('等待prd parser阶段人工反馈...');

  const feedback = interrupt({
    question: '检测到PRD信息不完整，请补充缺少的内容: ',
    prdAnalysis: state.prdAnalysis,
    action: '请选择: approve(批准) 或 revise(修改)',
    instructions: '如果需要修改，请在feedback字段中提供具体的修改建议',
  });

  // 当恢复执行时，这里的逻辑会被执行
  console.log('收到反馈:', feedback);

  // 根据反馈决定下一步
  if (feedback && feedback.includes('approve')) {
    return new Command({
      goto: NodeNames.PLANNER,
    });
  } else {
    // 如果需要修改，回到PRD_PARSER节点
    return new Command({
      goto: NodeNames.PRD_PARSER,
      update: {
        prdFeedback: feedback?.feedback || '',
      },
    });
  }
}

// Planner节点 - 生成任务
async function plannerNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('Planner节点开始生成TASK JSON...');

  const llm = createLLM();

  const promptText = `
你是一个专业的全栈工程师，你能基于用户输入的产品需求文档，分析完成前后端代码研发的链路，将产品需求文档进行格式化后，拆分成可执行的原子任务：

结构化PRD：
${state.structuredPRD}

${state.prdFeedback ? `人工反馈：${state.prdFeedback}` : ''}

${
  state.images && state.images.length > 0
    ? `
注意：PRD中包含以下图片信息，请在任务分解时考虑这些视觉内容：
${state.images.map((img, index) => `图片${index + 1}: ${img.url}${img.error ? ` (处理失败: ${img.error})` : ' (已处理)'}`).join('\n')}
如果图片包含原型图、界面设计或功能说明，请在任务的details字段中详细描述相关的实现要求。
`
    : ''
}

## 任务格式
每个任务应遵循以下JSON结构：
{
	"id": number,
	"title": string,
	"description": string,
	"status": "pending",
	"prompt": string,
	"dependencies": number[] （此任务依赖的任务ID）,
	"priority": "high" | "medium" | "low",
	"details": string （实现细节）,
	"testStrategy": string （验证方法）
}
## 技术栈
* 前端：基于amis的低码json。
* 后端：基于groovy的代码。

# 指导原则：
1. 除非复杂性需要，否则最多创建15个任务，从1开始依次编号
2. 每个任务应是原子性的，专注于单一职责，遵循最新的最佳实践和标准
3. 按逻辑顺序排列任务——考虑依赖关系和实现顺序
4. 初期任务应专注于设置、核心功能，然后是高级功能
5. 为每个任务包含清晰的验证/测试方法
6. 设置适当的依赖ID（一个任务只能依赖于具有较低ID的任务）
7. 根据重要性和依赖顺序分配优先级（高/中/低）
8. 在“details”字段中包含详细的实现指导，可以基于原型图把前端的样式细节，样式要求，页面结构啥的放在这。
9. 如果PRD包含有关库、数据库架构、框架、技术栈、原型图或任何其他实现细节的具体要求，请严格遵守这些要求进行任务分解，不要在任何情况下丢弃它们
10. 专注于填补PRD留下的空白或尚未完全指定的领域，同时保留所有明确的要求
11. 始终致力于提供最直接的实现路径，避免过度设计或迂回的方法。
# 响应限制
仅返回json 数组，不要使用markdown语法，类似这样：
[
  {
    "id": 1,
    "title": "设置项目结构",
    "description": "创建前后端项目的基本目录结构和配置文件",
    "status": "pending",
    "prompt": "请设置项目的基本目录结构和配置文件",
    "dependencies": [],
    "priority": "high",
    "details": "前端使用amis，后端使用groovy",
    "testStrategy": "检查目录结构和配置文件是否正确"
  },
  ...
]
`;

  try {
    // 如果有图片信息，使用包含图片的消息格式
    let response: AIMessage;
    if (state.images && state.images.length > 0) {
      const messageContent = createMessageWithImages(promptText, state.images);
      response = await llm.invoke([
        new HumanMessage({
          content: messageContent,
        }),
      ]);
    } else {
      response = await llm.invoke(promptText);
    }
    const taskJSON =
      typeof response.content === 'string'
        ? response.content
        : response.content.toString();

    console.log('Planner节点完成TASK JSON生成');

    return new Command({
      goto: NodeNames.PLANNER_FEEDBACK,
      update: {
        taskJSON: taskJSON,
      },
    });
  } catch (error) {
    console.error('Planner节点处理失败:', error);
    throw error;
  }
}

// 人工反馈节点 - Planner阶段
function planFeedbackNode(state: typeof StateAnnotation.State): Command {
  console.log('等待Planner阶段人工反馈...');

  const feedback = interrupt({
    question: '请审核TASK JSON和技术实现方案',
    taskJSON: state.taskJSON,
    structuredPRD: state.structuredPRD,
    action: '请选择: approve(批准) 或 revise(修改)',
    instructions: '如果需要修改，请在feedback字段中提供具体的修改建议',
  });

  // 当恢复执行时，这里的逻辑会被执行
  console.log('收到Planner反馈:', feedback);

  // 根据反馈决定下一步
  if (feedback && feedback.includes('approve')) {
    return new Command({
      goto: NodeNames.FINAL_RESPONSE,
    });
  } else {
    // 如果需要修改，回到Planner节点
    return new Command({
      goto: NodeNames.PLANNER,
      update: {
        planFeedback: feedback?.feedback || '',
      },
    });
  }
}

// 最终响应节点 - 生成完整的结构化响应
async function finalResponseNode(
  state: typeof StateAnnotation.State,
): Promise<Command> {
  console.log('FinalResponse节点生成最终响应...');

  // 构建完整的响应消息
  const fullResponse = {
    isPRDRequest: state.isPRDRequest,
    originalPRD: state.originalPRD,
    structuredPRD: state.structuredPRD,
    taskJSON: state.taskJSON,
    images: state.images || [],
    documentContent: state.documentContent,
  };

  return new Command({
    goto: END,
  });
}

// 创建并导出图
export function createGraph() {
  // 创建内存检查点
  const checkpointer = new MemorySaver();

  // 构建工作流图
  const graph = new StateGraph(StateAnnotation)
    .addNode(NodeNames.COORDINATOR, coordinatorNode, {
      ends: [NodeNames.TOOLS, NodeNames.PRD_PARSER, END],
    })
    .addNode(NodeNames.TOOLS, toolsNode, {
      ends: [NodeNames.COORDINATOR, NodeNames.PRD_PARSER],
    })
    .addNode(NodeNames.PRD_PARSER, prdParserNode, {
      ends: [NodeNames.PLANNER, NodeNames.PRD_FEEDBACK],
    })
    .addNode(NodeNames.PRD_FEEDBACK, prdFeedbackNode, {
      ends: [NodeNames.PLANNER, NodeNames.PRD_PARSER],
    })
    .addNode(NodeNames.PLANNER, plannerNode, {
      ends: [NodeNames.PLANNER_FEEDBACK],
    })
    .addNode(NodeNames.PLANNER_FEEDBACK, planFeedbackNode, {
      ends: [NodeNames.PLANNER, NodeNames.FINAL_RESPONSE],
    })
    .addNode(NodeNames.FINAL_RESPONSE, finalResponseNode, {
      ends: [END],
    })
    .addEdge(START, NodeNames.COORDINATOR)
    .compile({ checkpointer });

  return graph;
}

// 默认导出图实例
export default createGraph();
